package main

import (
	"database/sql"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"

	_ "github.com/lib/pq"
)

// Config structure for database connection
type Config struct {
	DBHost     string `json:"db_host"`
	DBPort     int    `json:"db_port"`
	DBUser     string `json:"db_user"`
	DBPassword string `json:"db_password"`
	DBName     string `json:"db_name"`
	FolderPath string `json:"folder_path"`
}

// Animation data structure for JSON output
type AnimationData struct {
	Pid         int    `json:"pid"`
	Job         int    `json:"job"`
	Zx          int    `json:"zx"`
	EffectID    int    `json:"effect_id"`
	HitCount    int    `json:"hit_count"`
	HitTimes    []int  `json:"hit_times"`
	HitEndTimes []int  `json:"hit_end_times"`
	FileName    string `json:"file_name"`
}

type Record struct {
	FldPid    int    `json:"fld_pid"`
	FldJob    int    `json:"fld_job"`
	FldZx     int    `json:"fld_zx"`
	FldEffert int    `json:"fld_effert"`
	FileName  string `json:"file_name"`
}

// Job mapping
var jobMap = map[int]string{
	1:  "K",
	2:  "S",
	3:  "B",
	4:  "A",
	5:  "E",
	6:  "D",
	7:  "M",
	8:  "V",
	9:  "H",
	10: "Z",
	11: "U",
	12: "O",
	13: "G",
}

func main() {
	// Test with existing .ani files in current directory
	config, err := loadConfig()
	if err != nil {
		fmt.Printf("Error loading config: %v\n", err)
		return
	}

	// arguments switches
	if len(os.Args) > 1 && os.Args[1] == "-normal" {
		// scan all .ani files start with MC in config.FolderPath and print their hit times
		files, err := os.ReadDir(config.FolderPath)
		if err != nil {
			fmt.Printf("Error reading folder: %v\n", err)
			return
		}
		var animationResults []AnimationData
		for _, file := range files {
			if file.IsDir() {
				continue
			}
			fileName := file.Name()
			if fileName[:2] != "MC" {
				continue
			}

			job, effect, err := parseFileName(fileName)
			if err != nil {
				fmt.Printf("Error parsing file name %s: %v\n", fileName, err)
				continue
			}

			hitCount, hitTimes, hitEndTimes, err := parseAniFile(config.FolderPath + "\\" + fileName)
			if err != nil {
				fmt.Printf("Error parsing file %s: %v\n", fileName, err)
				continue
			}
			// fmt.Printf("File %s: %d hit(s)\n", fileName, hitCount)
			for i := 0; i < hitCount; i++ {
				fmt.Printf("Hit %d: %d - %d\n", i+1, hitTimes[i], hitEndTimes[i])
			}
			if hitCount == 0 {
				continue
			}
			result := AnimationData{
				Pid:         0,
				Job:         job,
				Zx:          0,
				EffectID:    effect,
				HitCount:    hitCount,
				HitTimes:    hitTimes,
				HitEndTimes: hitEndTimes,
				FileName:    fileName,
			}
			animationResults = append(animationResults, result)
		}
		// Generate C# dictionary format
		// csharpContent := generateCSharpDictionary(animationResults)

		// // write to file output/normal.cs
		// println("Total files: " + strconv.Itoa(len(animationResults)))
		// err = os.WriteFile("output/AttackAnimation.cs", []byte(csharpContent), 0644)
		// if err != nil {
		// 	fmt.Printf("Error writing file: %v\n", err)
		// 	return
		// }

	} else if len(os.Args) > 1 && os.Args[1] == "-export" {
		// Common export mode for both file and database AnimationData
		exportCommonAnimationData(config)
	} else {
		kongfu, err := queryDatabase(config)
		if err != nil {
			fmt.Printf("Error querying database: %v\n", err)
			return
		}
		// print all kongfu
		fmt.Println(kongfu)

		var animationResults []AnimationData
		var errorFiles []AnimationData
		var updateCount int

		// Kết nối database để update
		db, err := connectDatabase(config)
		if err != nil {
			fmt.Printf("Error connecting to database: %v\n", err)
			return
		}
		defer db.Close()

		for _, kf := range kongfu {
			hitCount, hitTimes, hitEndTimes, err := parseAniFile(config.FolderPath + "\\" + kf.FileName)
			if err != nil {
				fmt.Printf("Error parsing file %s: %v\n", kf.FileName, err)

				fmt.Printf("Process new file name: " + kf.FileName)
				if kf.FileName[:2] == "MC" {
					kf.FileName = strings.Replace(kf.FileName, "MC", "MF", 1)
				}
				hitCount, hitTimes, hitEndTimes, err = parseAniFile(config.FolderPath + "\\" + kf.FileName)
				if err != nil {
					fmt.Printf("Error parsing file %s: %v\n", kf.FileName, err)
					result := AnimationData{
						Pid:         kf.FldPid,
						Job:         kf.FldJob,
						Zx:          kf.FldZx,
						EffectID:    kf.FldEffert,
						HitCount:    0,
						HitTimes:    []int{},
						HitEndTimes: []int{},
						FileName:    kf.FileName,
					}
					errorFiles = append(errorFiles, result)
					continue
				}

			}

			result := AnimationData{
				Pid:         kf.FldPid,
				Job:         kf.FldJob,
				Zx:          kf.FldZx,
				EffectID:    kf.FldEffert,
				HitCount:    hitCount,
				HitTimes:    hitTimes,
				HitEndTimes: hitEndTimes,
				FileName:    kf.FileName,
			}

			// Update database với hit times data
			// err = updateHitTimesInDB(db, kf.FldPid, kf.FldJob, kf.FldZx, kf.FldEffert, hitCount, hitTimes, hitEndTimes)
			// if err != nil {
			// 	fmt.Printf("Error updating database for %s: %v\n", kf.FileName, err)
			// } else {
			// 	updateCount++
			// 	fmt.Printf("Updated hit times for %s (PID: %d)\n", kf.FileName, kf.FldPid)
			// }

			animationResults = append(animationResults, result)
		}

		fmt.Printf("\nSummary: Updated %d records successfully\n", updateCount)
		fmt.Printf("Errors: %d files\n", len(errorFiles))

		// Output JSON results
		outputJSON, err := json.MarshalIndent(animationResults, "", "  ")
		if err != nil {
			fmt.Printf("Error marshaling JSON: %v\n", err)
			return
		}

		fmt.Println("\nResults:")
		fmt.Println(string(outputJSON))
		// write to file output/output.json
		err = os.WriteFile("output/output.json", outputJSON, 0644)
		if err != nil {
			fmt.Printf("Error writing file: %v\n", err)
			return
		}
		// write error files to file output/error.json
		errorJSON, err := json.MarshalIndent(errorFiles, "", "  ")
		if err != nil {
			fmt.Printf("Error marshaling JSON: %v\n", err)
			return
		}
		err = os.WriteFile("output/error.json", errorJSON, 0644)
		if err != nil {
			fmt.Printf("Error writing file: %v\n", err)
			return
		}
	}

}

func loadConfig() (Config, error) {
	configFile, err := os.Open("config.json")
	if err != nil {
		return Config{}, err
	}
	defer configFile.Close()

	var config Config
	err = json.NewDecoder(configFile).Decode(&config)
	if err != nil {
		return Config{}, err
	}

	return config, nil
}

func connectDatabase(config Config) (*sql.DB, error) {
	connString := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable", config.DBHost, config.DBPort, config.DBUser, config.DBPassword, config.DBName)
	db, err := sql.Open("postgres", connString)
	if err != nil {
		return nil, err
	}

	// Test connection
	err = db.Ping()
	if err != nil {
		db.Close()
		return nil, err
	}

	return db, nil
}

func queryDatabase(config Config) ([]Record, error) {
	db, err := connectDatabase(config)
	if err != nil {
		return []Record{}, err
	}
	defer db.Close()

	query := "SELECT fld_pid, fld_job, fld_zx, fld_effert FROM public.tbl_xwwl_kongfu WHERE fld_effert != 0"
	rows, err := db.Query(query)
	if err != nil {
		return []Record{}, err
	}
	defer rows.Close()

	var records []Record
	for rows.Next() {
		var record Record
		err := rows.Scan(&record.FldPid, &record.FldJob, &record.FldZx, &record.FldEffert)
		if err != nil {
			return []Record{}, err
		}
		record.FileName = buildFileName(record) // Build file name based on record data
		records = append(records, record)
	}
	return records, nil
}

func updateHitTimesInDB(db *sql.DB, pid, job, zx, effert, hitCount int, hitTimes, hitEndTimes []int) error {
	// Convert arrays to JSON
	hitTimesJSON, err := json.Marshal(hitTimes)
	if err != nil {
		return fmt.Errorf("error marshaling hit_times: %v", err)
	}

	hitEndTimesJSON, err := json.Marshal(hitEndTimes)
	if err != nil {
		return fmt.Errorf("error marshaling hit_end_times: %v", err)
	}

	// Update query
	query := `
		UPDATE public.tbl_xwwl_kongfu
		SET fld_hit_times = $1,
		    fld_hit_end_times = $2
		WHERE fld_pid = $3 AND fld_job = $4 AND fld_zx = $5 AND fld_effert = $6`

	result, err := db.Exec(query, string(hitTimesJSON), string(hitEndTimesJSON), pid, job, zx, effert)
	if err != nil {
		return fmt.Errorf("error executing update query: %v", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("error getting rows affected: %v", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("no rows updated for pid=%d, job=%d, zx=%d, effert=%d", pid, job, zx, effert)
	}

	return nil
}

func buildFileName(record Record) string {
	jobChar := jobMap[record.FldJob]
	effectStr := fmt.Sprintf("%04d", record.FldEffert)
	if record.FldZx == 0 {
		return fmt.Sprintf("MC%s%s.ani", jobChar, effectStr)
	} else if record.FldZx == 1 {
		return fmt.Sprintf("AMC%s%s.ani", jobChar, effectStr)
	} else if record.FldZx == 2 {
		return fmt.Sprintf("BMC%s%s.ani", jobChar, effectStr)
	}
	return ""
}

func parseFileName(fileName string) (int, int, error) {
	// Extract job character and effect ID from file name
	var jobChar byte
	var effectStr string

	if len(fileName) >= 7 && fileName[:2] == "MC" {
		// MC files: MCX####.ani
		jobChar = fileName[2]
		effectStr = fileName[3:7]
	} else if len(fileName) >= 8 && (fileName[:3] == "AMC" || fileName[:3] == "BMC") {
		// AMC/BMC files: AMCX####.ani or BMCX####.ani
		jobChar = fileName[3]
		effectStr = fileName[4:8]
	} else {
		return 0, 0, fmt.Errorf("invalid file name format: %s", fileName)
	}

	effectInt, err := strconv.Atoi(effectStr)
	if err != nil {
		return 0, 0, fmt.Errorf("invalid effect ID: %s", effectStr)
	}

	for job, char := range jobMap {
		if char == string(jobChar) {
			return job, effectInt, nil
		}
	}

	return 0, 0, fmt.Errorf("unknown job character: %c", jobChar)
}

func parseAniFile(filePath string) (int, []int, []int, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return 0, nil, nil, err
	}

	if len(data) < 12 {
		return 0, nil, nil, fmt.Errorf("file too small: %d bytes", len(data))
	}

	// Read first 4 bytes as int32 LE
	header := binary.LittleEndian.Uint32(data[0:4])
	var dataStart int
	var hitCount int
	var maxCount int
	if header == 0 {
		// Data starts at byte 8
		dataStart = 8
		maxCount = 3
		hitCount = int(binary.LittleEndian.Uint32(data[dataStart : dataStart+4]))
	} else if header == 100000 {
		// Data starts at byte 12 (0xC)
		dataStart = 12
		maxCount = 5
		hitCount = int(binary.LittleEndian.Uint32(data[dataStart : dataStart+4]))
	} else {
		return 0, nil, nil, fmt.Errorf("unknown header value: %d", header)
	}

	// Check if we have enough data
	if len(data) < dataStart+hitCount*4 {
		return 0, nil, nil, fmt.Errorf("file too small for expected data: have %d bytes, need %d bytes",
			len(data), dataStart+hitCount*4)
	}

	// Read hit times
	hitTimes := make([]int, hitCount)
	hitEndTimes := make([]int, hitCount)
	for i := 0; i < hitCount; i++ {
		start := dataStart + 4 + i*4
		hitTimes[i] = int(binary.LittleEndian.Uint32(data[start : start+4]))
		hitEndTimes[i] = int(binary.LittleEndian.Uint32(data[start+maxCount*4 : start+4+maxCount*4]))
	}

	return hitCount, hitTimes, hitEndTimes, nil
}

func exportCommonAnimationData(config Config) {
	var allAnimationData []AnimationData

	// First, get data from files (normal mode)
	files, err := os.ReadDir(config.FolderPath)
	if err != nil {
		fmt.Printf("Error reading folder: %v\n", err)
		return
	}

	for _, file := range files {
		if file.IsDir() {
			continue
		}
		fileName := file.Name()
		// if len(fileName) < 3 || (fileName[:2] != "MC" && fileName[:3] != "AMC" && fileName[:3] != "BMC") {
		if len(fileName) < 3 || (fileName[:2] != "MC") {
			continue
		}

		job, effect, err := parseFileName(fileName)
		if err != nil {
			fmt.Printf("Error parsing file name %s: %v\n", fileName, err)
			continue
		}

		hitCount, hitTimes, hitEndTimes, err := parseAniFile(config.FolderPath + "\\" + fileName)
		if err != nil {
			fmt.Printf("Error parsing file %s: %v\n", fileName, err)
			continue
		}

		if hitCount == 0 {
			continue
		}

		// Determine zx from filename prefix
		var zx int
		if fileName[:3] == "AMC" {
			zx = 1
		} else if fileName[:3] == "BMC" {
			zx = 2
		} else {
			zx = 0
		}

		result := AnimationData{
			Pid:         0,
			Job:         job,
			Zx:          zx,
			EffectID:    effect,
			HitCount:    hitCount,
			HitTimes:    hitTimes,
			HitEndTimes: hitEndTimes,
			FileName:    fileName,
		}
		allAnimationData = append(allAnimationData, result)
	}

	// Second, get data from database
	kongfu, err := queryDatabase(config)
	if err != nil {
		fmt.Printf("Error querying database: %v\n", err)
		return
	}

	for _, kf := range kongfu {
		hitCount, hitTimes, hitEndTimes, err := parseAniFile(config.FolderPath + "\\" + kf.FileName)
		if err != nil {
			fmt.Printf("Error parsing file %s: %v\n", kf.FileName, err)
			continue
		}

		result := AnimationData{
			Pid:         kf.FldPid,
			Job:         kf.FldJob,
			Zx:          kf.FldZx,
			EffectID:    kf.FldEffert,
			HitCount:    hitCount,
			HitTimes:    hitTimes,
			HitEndTimes: hitEndTimes,
			FileName:    kf.FileName,
		}
		allAnimationData = append(allAnimationData, result)
	}

	// Generate C# file with tuple key (job, effectId, zx)
	csharpContent := generateCSharpDictionaryWithTuple(allAnimationData)

	// Write to output file
	fmt.Printf("Total animation data entries: %d\n", len(allAnimationData))
	err = os.WriteFile("output/AnimationData.cs", []byte(csharpContent), 0644)
	if err != nil {
		fmt.Printf("Error writing file: %v\n", err)
		return
	}

	fmt.Println("Successfully exported combined animation data to output/CombinedAnimationData.cs")
}

func generateCSharpDictionaryWithTuple(animationResults []AnimationData) string {
	var result string

	result += "using System;\n"
	result += "using System.Collections.Generic;\n\n"
	result += "public static class AnimationData\n"
	result += "{\n"
	result += "    public static Dictionary<(int job, int effectId, int zx), AnimationInfo> Data = new Dictionary<(int job, int effectId, int zx), AnimationInfo>\n"
	result += "    {\n"

	uniqueMap := make(map[[3]int]AnimationData)
	for _, anim := range animationResults {
		key := [3]int{anim.Job, anim.EffectID, anim.Zx}
		if _, exists := uniqueMap[key]; !exists {
			uniqueMap[key] = anim
		}
	}

	i := 0
	for _, anim := range uniqueMap {
		result += fmt.Sprintf("        { (%d, %d, %d), new AnimationInfo { HitCount = %d, HitTimes = new int[] { %s }, HitEndTimes = new int[] { %s }, FileName = \"%s\" } }",
			anim.Job, anim.EffectID, anim.Zx, anim.HitCount,
			formatIntArray(anim.HitTimes),
			formatIntArray(anim.HitEndTimes),
			anim.FileName)

		if i < len(uniqueMap)-1 {
			result += ","
		}
		result += "\n"
		i++
	}

	result += "    };\n"
	result += "}\n\n"
	result += "public class AnimationInfo\n"
	result += "{\n"
	result += "    public int HitCount { get; set; }\n"
	result += "    public int[] HitTimes { get; set; }\n"
	result += "    public int[] HitEndTimes { get; set; }\n"
	result += "    public string FileName { get; set; }\n"
	result += "}\n"

	return result
}

func formatIntArray(arr []int) string {
	if len(arr) == 0 {
		return ""
	}

	var result string
	for i, val := range arr {
		result += strconv.Itoa(val)
		if i < len(arr)-1 {
			result += ", "
		}
	}
	return result
}
