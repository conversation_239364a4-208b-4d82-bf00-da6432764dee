package main

import (
	"database/sql"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"os"

	_ "github.com/lib/pq"
)

// Config structure for database connection
type Config struct {
	DBHost     string `json:"db_host"`
	DBPort     int    `json:"db_port"`
	DBUser     string `json:"db_user"`
	DBPassword string `json:"db_password"`
	DBName     string `json:"db_name"`
	FolderPath string `json:"folder_path"`
}

// Animation data structure for JSON output
type AnimationData struct {
	Pid         int    `json:"pid"`
	Job         int    `json:"job"`
	Zx          int    `json:"zx"`
	EffectID    int    `json:"effect_id"`
	HitCount    int    `json:"hit_count"`
	HitTimes    []int  `json:"hit_times"`
	HitEndTimes []int  `json:"hit_end_times"`
	FileName    string `json:"file_name"`
}

type Record struct {
	FldPid    int    `json:"fld_pid"`
	FldJob    int    `json:"fld_job"`
	FldZx     int    `json:"fld_zx"`
	FldEffert int    `json:"fld_effert"`
	FileName  string `json:"file_name"`
}

// Job mapping
var jobMap = map[int]string{
	1:  "K",
	2:  "S",
	3:  "B",
	4:  "A",
	5:  "E",
	6:  "D",
	7:  "M",
	8:  "V",
	9:  "H",
	10: "Z",
	11: "U",
	12: "O",
	13: "G",
}

func main() {
	// Test with existing .ani files in current directory
	config, err := loadConfig()
	if err != nil {
		fmt.Printf("Error loading config: %v\n", err)
		return
	}
	kongfu, err := queryDatabase(config)
	if err != nil {
		fmt.Printf("Error querying database: %v\n", err)
		return
	}
	// print all kongfu
	fmt.Println(kongfu)
	// files := []string{"MCK0201.ani", "MCK0202.ani", "AMCK0306.ani"}

	var animationResults []AnimationData
	var errorFiles []string

	for _, kf := range kongfu {
		hitCount, hitTimes, hitEndTimes, err := parseAniFile(config.FolderPath + "\\" + kf.FileName)
		if err != nil {
			fmt.Printf("Error parsing file %s: %v\n", kf.FileName, err)
			errorFiles = append(errorFiles, kf.FileName)
			continue
		}

		result := AnimationData{
			Pid:         kf.FldPid,
			Job:         kf.FldJob,
			Zx:          kf.FldZx,
			EffectID:    kf.FldEffert,
			HitCount:    hitCount,
			HitTimes:    hitTimes,
			HitEndTimes: hitEndTimes,
			FileName:    kf.FileName,
		}

		animationResults = append(animationResults, result)
	}

	// Output JSON results
	outputJSON, err := json.MarshalIndent(animationResults, "", "  ")
	if err != nil {
		fmt.Printf("Error marshaling JSON: %v\n", err)
		return
	}

	fmt.Println("\nResults:")
	fmt.Println(string(outputJSON))
	// write to file output/output.json
	err = os.WriteFile("output/output.json", outputJSON, 0644)
	if err != nil {
		fmt.Printf("Error writing file: %v\n", err)
		return
	}
	// write error files to file output/error.json
	errorJSON, err := json.MarshalIndent(errorFiles, "", "  ")
	if err != nil {
		fmt.Printf("Error marshaling JSON: %v\n", err)
		return
	}
	err = os.WriteFile("output/error.json", errorJSON, 0644)
	if err != nil {
		fmt.Printf("Error writing file: %v\n", err)
		return
	}
}

func loadConfig() (Config, error) {
	configFile, err := os.Open("config.json")
	if err != nil {
		return Config{}, err
	}
	defer configFile.Close()

	var config Config
	err = json.NewDecoder(configFile).Decode(&config)
	if err != nil {
		return Config{}, err
	}

	return config, nil
}

func queryDatabase(config Config) ([]Record, error) {
	// connect to postgres
	connString := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable", config.DBHost, config.DBPort, config.DBUser, config.DBPassword, config.DBName)
	db, err := sql.Open("postgres", connString)
	if err != nil {
		return []Record{}, err
	}
	defer db.Close()

	query := "SELECT fld_pid, fld_job, fld_zx, fld_effert FROM public.tbl_xwwl_kongfu WHERE fld_effert != 0"
	rows, err := db.Query(query)
	if err != nil {
		return []Record{}, err
	}
	var records []Record
	for rows.Next() {
		var record Record
		err := rows.Scan(&record.FldPid, &record.FldJob, &record.FldZx, &record.FldEffert)
		if err != nil {
			return []Record{}, err
		}
		record.FileName = buildFileName(record) // Build file name based on record data
		records = append(records, record)
	}
	return records, nil
}

func buildFileName(record Record) string {
	jobChar := jobMap[record.FldJob]
	effectStr := fmt.Sprintf("%04d", record.FldEffert)
	if record.FldZx == 0 {
		return fmt.Sprintf("MC%s%s.ani", jobChar, effectStr)
	} else if record.FldZx == 1 {
		return fmt.Sprintf("AMC%s%s.ani", jobChar, effectStr)
	} else if record.FldZx == 2 {
		return fmt.Sprintf("BMC%s%s.ani", jobChar, effectStr)
	}
	return ""
}

func parseAniFile(filePath string) (int, []int, []int, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return 0, nil, nil, err
	}

	if len(data) < 12 {
		return 0, nil, nil, fmt.Errorf("file too small: %d bytes", len(data))
	}

	// Read first 4 bytes as int32 LE
	header := binary.LittleEndian.Uint32(data[0:4])
	var dataStart int
	var hitCount int
	var maxCount int
	if header == 0 {
		// Data starts at byte 8
		dataStart = 8
		maxCount = 3
		hitCount = int(binary.LittleEndian.Uint32(data[dataStart : dataStart+4]))
	} else if header == 100000 {
		// Data starts at byte 12 (0xC)
		dataStart = 12
		maxCount = 5
		hitCount = int(binary.LittleEndian.Uint32(data[dataStart : dataStart+4]))
	} else {
		return 0, nil, nil, fmt.Errorf("unknown header value: %d", header)
	}

	// Check if we have enough data
	if len(data) < dataStart+hitCount*4 {
		return 0, nil, nil, fmt.Errorf("file too small for expected data: have %d bytes, need %d bytes",
			len(data), dataStart+hitCount*4)
	}

	// Read hit times
	hitTimes := make([]int, hitCount)
	hitEndTimes := make([]int, hitCount)
	for i := 0; i < hitCount; i++ {
		start := dataStart + 4 + i*4
		hitTimes[i] = int(binary.LittleEndian.Uint32(data[start : start+4]))
		hitEndTimes[i] = int(binary.LittleEndian.Uint32(data[start+maxCount*4 : start+4+maxCount*4]))
	}

	return hitCount, hitTimes, hitEndTimes, nil
}
